<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Command\Organization\Update\UpdateOrganizationCommand;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Put(
    path: '/tenants/v1/{_locale}/organizations/{id}',
    summary: 'Update Organization',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'id',
            description: 'Organization ID',
            in: 'path',
            required: true,
            schema: new OA\Schema(type: 'string', format: 'uuid')
        ),
    ],
    requestBody: new OA\RequestBody(
        description: 'Organization data',
        required: true,
        content: new OA\JsonContent(
            required: ['name', 'email', 'address'],
            properties: [
                new OA\Property(property: 'name', type: 'string', minLength: 3, maxLength: 100, description: 'Organization name'),
                new OA\Property(property: 'email', type: 'string', format: 'email', description: 'Valid email address'),
                new OA\Property(property: 'phone', type: 'string', description: 'Phone number in international format', nullable: true),
                new OA\Property(property: 'address', type: 'string', description: 'Full address as plain text'),
            ],
            example: [
                'name' => 'ACME Holdings Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Business Ave, New York, NY 10001, US'
            ]
        )
    ),
    responses: [
        new OA\Response(
            response: Response::HTTP_OK,
            description: 'Organization updated successfully',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_NOT_FOUND,
            description: 'Organization not found'
        ),
        new OA\Response(
            response: Response::HTTP_BAD_REQUEST,
            description: 'Validation error'
        )
    ]
)]
class UpdateOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $command,
        private readonly RequestMapper $requestMapper,
        private readonly ResponseMapper $responseMapper
    ) {}

    #[Route('/organizations/{id}', name: 'app_update_organization', methods: ['PUT'])]
    public function __invoke(Request $request, string $id): JsonResponse
    {
        /** @var UpdateOrganizationCommand $command */
        $command = $this->requestMapper->fromRequest($request, UpdateOrganizationCommand::class, ['id' => $id]);

        $this->command->dispatch($command);

        $result = $command->getUpdatedOrganization();

        if (null === $result) {
            return $this->responseMapper->serializeResponse(null, Response::HTTP_NOT_FOUND);
        }

        return $this->responseMapper->serializeResponse($result, Response::HTTP_OK);
    }
}
