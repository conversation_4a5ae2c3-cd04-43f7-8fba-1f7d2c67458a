<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Query\Organization\Get\GetOrganizationQuery;
use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Get(
    path: '/tenants/v1/{_locale}/organizations/{id}',
    summary: 'Get Organization',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
        new OA\Parameter(
            name: 'id',
            description: 'Organization ID',
            in: 'path',
            required: true,
            schema: new OA\Schema(type: 'string', format: 'uuid')
        ),
    ],
    responses: [
        new OA\Response(
            response: Response::HTTP_OK,
            description: 'Organization found',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_NOT_FOUND,
            description: 'Organization not found'
        )
    ]
)]
class GetOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly QueryBusInterface $query,
        private readonly ResponseMapper $responseMapper
    ) {}

    #[Route('/organizations/{id}', name: 'app_get_organization', methods: ['GET'])]
    public function __invoke(string $id): JsonResponse
    {
        /** @var GetOrganizationQueryResponse|null $response */
        $response = $this->query->ask(new GetOrganizationQuery($id));

        if (null === $response) {
            return $this->responseMapper->serializeResponse(null, Response::HTTP_NOT_FOUND);
        }

        return $this->responseMapper->serializeResponse($response, Response::HTTP_OK);
    }
}
