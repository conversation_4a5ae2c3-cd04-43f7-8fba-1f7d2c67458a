<?php

declare(strict_types=1);

namespace App\Infrastructure\Delivery\Api\V1\Organization;

use OpenApi\Attributes as OA;
use App\Application\Command\Organization\Create\CreateOrganizationCommand;
use App\Application\Query\Organization\Get\GetOrganizationQuery;
use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;
use App\Domain\Model\Bus\Command\CommandBusInterface;
use App\Domain\Model\Bus\Query\QueryBusInterface;
use App\Infrastructure\Http\RequestMapper;
use App\Infrastructure\Http\ResponseMapper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[OA\Post(
    path: '/tenants/v1/{_locale}/organizations',
    summary: 'Create Organization',
    security: [['bearerAuth' => []]],
    tags: ['Organizations'],
    parameters: [
        new OA\Parameter(ref: '#/components/parameters/_locale'),
    ],
    requestBody: new OA\RequestBody(
        description: 'Organization data',
        required: true,
        content: new OA\JsonContent(
            required: ['name', 'email', 'address'],
            properties: [
                new OA\Property(property: 'name', type: 'string', minLength: 3, maxLength: 100, description: 'Organization name'),
                new OA\Property(property: 'email', type: 'string', format: 'email', description: 'Valid email address'),
                new OA\Property(property: 'phone', type: 'string', description: 'Phone number in international format', nullable: true),
                new OA\Property(property: 'address', type: 'string', description: 'Full address as plain text'),
            ],
            example: [
                'name' => 'ACME Holdings Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Business Ave, New York, NY 10001, US'
            ]
        )
    ),
    responses: [
        new OA\Response(
            response: Response::HTTP_CREATED,
            description: 'Organization created successfully',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'data', ref: '#/components/schemas/OrganizationResponse')
                ]
            )
        ),
        new OA\Response(
            response: Response::HTTP_BAD_REQUEST,
            description: 'Validation error'
        )
    ]
)]
class CreateOrganizationAction extends AbstractController
{
    public function __construct(
        private readonly CommandBusInterface $command,
        private readonly QueryBusInterface $query,
        private readonly RequestMapper $requestMapper,
        private readonly ResponseMapper $responseMapper
    ) {}

    #[Route('/organizations', name: 'app_create_organization', methods: ['POST'])]
    public function __invoke(Request $request): JsonResponse
    {
        /** @var CreateOrganizationCommand $command */
        $command = $this->requestMapper->fromRequest($request, CreateOrganizationCommand::class);

        $this->command->dispatch($command);

        /** @var GetOrganizationQueryResponse|null $organization */
        $organization = $this->query->ask(new GetOrganizationQuery($command->getOrganizationId()));

        if (null === $organization) {
            return $this->responseMapper->serializeResponse(null, Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $this->responseMapper->serializeResponse($organization, Response::HTTP_CREATED);
    }
}
