<?php

declare(strict_types=1);

namespace App\Infrastructure\Domain\Model\Organization;

use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class DoctrineOrganizationRepository extends ServiceEntityRepository implements OrganizationRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Organization::class);
    }

    public function save(Organization $organization): void
    {
        $this->getEntityManager()->persist($organization);
        $this->getEntityManager()->flush();
    }

    public function find($id, $lockMode = null, ?int $lockVersion = null): ?Organization
    {
        return $this->findOneBy(['id' => $id]);
    }

    public function findByName(string $name): ?Organization
    {
        return $this->findOneBy(['name' => $name]);
    }

    public function findByEmail(string $email): ?Organization
    {
        return $this->findOneBy(['email' => $email]);
    }

    public function findByPhone(string $phone): ?Organization
    {
        return $this->findOneBy(['phone' => $phone]);
    }

    public function findByWebsite(string $website): ?Organization
    {
        return $this->findOneBy(['website' => $website]);
    }

    /**
     * @return Organization[]
     */
    public function findAll(): array
    {
        return $this->findBy([]);
    }

    public function remove(Organization $organization): void
    {
        $this->getEntityManager()->remove($organization);
        $this->getEntityManager()->flush();
    }
}
