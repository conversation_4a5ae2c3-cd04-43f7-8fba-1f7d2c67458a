<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:gedmo="http://gediminasm.org/schemas/orm/doctrine-extensions-mapping"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="App\Domain\Model\Organization\Organization" table="organizations">
        <id name="id" type="guid" column="id">
            <options>
                <option name="fixed">true</option>
            </options>
        </id>
        <field name="name" type="string" length="100" nullable="false"/>
        <field name="email" type="string" length="255" nullable="false"/>
        <field name="phone" type="string" length="20" nullable="false"/>
        <field name="address" type="text" nullable="false"/>
        <field name="deletedAt" type="datetime" column="deleted_at" nullable="true"/>
        <gedmo:soft-deleteable field-name="deletedAt" time-aware="false" hard-delete="true"/>

        <field name="updatedAt" type="datetime" column="updated_at" nullable="true">
            <gedmo:timestampable on="update"/>
        </field>
        <field name="createdAt" type="datetime" column="created_at" nullable="true">
            <gedmo:timestampable on="create"/>
        </field>

        <index name="idx_organization_email">
            <column name="email"/>
        </index>
        <index name="idx_organization_phone">
            <column name="phone"/>
        </index>
        <index name="idx_organization_deleted_at">
            <column name="deleted_at"/>
        </index>
    </entity>
</doctrine-mapping>
