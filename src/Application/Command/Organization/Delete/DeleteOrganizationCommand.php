<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Delete;

use App\Domain\Model\Bus\Command\CommandInterface;

final class DeleteOrganizationCommand implements CommandInterface
{
    private bool $deleted = false;

    public function __construct(
        private readonly string $id
    ) {}

    public function getId(): string
    {
        return $this->id;
    }

    public function setDeleted(bool $deleted): void
    {
        $this->deleted = $deleted;
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }
}
