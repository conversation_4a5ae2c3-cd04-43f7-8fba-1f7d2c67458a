<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Delete;

use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class DeleteOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository
    ) {}

    public function __invoke(DeleteOrganizationCommand $command): void
    {
        $organization = $this->organizationRepository->find($command->getId());

        if (null === $organization) {
            $command->setDeleted(false);
            return;
        }

        $organization->markAsDeleted();
        $this->organizationRepository->save($organization);

        $command->setDeleted(true);
    }
}
