<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Update;

use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class UpdateOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository
    ) {}

    public function __invoke(UpdateOrganizationCommand $command): void
    {
        $organization = $this->organizationRepository->find($command->getId());

        if (null === $organization) {
            $command->setUpdatedOrganization(null);
            return;
        }

        $organization->update(
            $command->getName(),
            $command->getEmail(),
            $command->getPhone(),
            $command->getAddress()
        );

        $this->organizationRepository->save($organization);

        $updatedOrganization = new GetOrganizationQueryResponse(
            $organization->getId(),
            $organization->getName(),
            $organization->getEmail(),
            $organization->getPhone(),
            $organization->getAddress(),
            $organization->getDeletedAt(),
            $organization->getCreatedAt(),
            $organization->getUpdatedAt()
        );

        $command->setUpdatedOrganization($updatedOrganization);
    }
}
