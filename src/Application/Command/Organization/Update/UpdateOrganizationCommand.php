<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Update;

use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;
use App\Domain\Model\Bus\Command\CommandInterface;

final class UpdateOrganizationCommand implements CommandInterface
{
    private ?GetOrganizationQueryResponse $updatedOrganization = null;

    public function __construct(
        private readonly string $id,
        private readonly string $name,
        private readonly string $email,
        private readonly ?string $phone,
        private readonly string $address
    ) {}

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setUpdatedOrganization(?GetOrganizationQueryResponse $organization): void
    {
        $this->updatedOrganization = $organization;
    }

    public function getUpdatedOrganization(): ?GetOrganizationQueryResponse
    {
        return $this->updatedOrganization;
    }
}
