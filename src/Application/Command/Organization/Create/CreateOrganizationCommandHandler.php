<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Create;

use App\Domain\Model\Organization\Organization;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class CreateOrganizationCommandHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $organizationRepository
    ) {}

    public function __invoke(CreateOrganizationCommand $command): void
    {
        $organization = new Organization(
            $command->getName(),
            $command->getEmail(),
            $command->getPhone(),
            $command->getAddress()
        );

        $this->organizationRepository->save($organization);
        $command->setOrganizationId($organization->getId());
    }
}
