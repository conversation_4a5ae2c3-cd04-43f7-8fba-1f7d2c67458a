<?php

declare(strict_types=1);

namespace App\Application\Command\Organization\Create;

use App\Domain\Model\Bus\Command\CommandInterface;

final readonly class CreateOrganizationCommand implements CommandInterface
{
    public function __construct(
        private string $name,
        private string $email,
        private ?string $phone,
        private string $address
    ) {}

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getAddress(): string
    {
        return $this->address;
    }
}
