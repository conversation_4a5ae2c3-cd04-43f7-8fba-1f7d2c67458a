<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\Get;

use OpenApi\Attributes as OA;
use App\Domain\Model\Bus\Query\QueryResponseInterface;
use DateTimeInterface;

#[OA\Schema(
    schema: 'OrganizationResponse',
    type: 'object',
    required: ['id', 'name', 'email', 'address', 'deletedAt', 'createdAt', 'updatedAt'],
    properties: [
        new OA\Property(property: 'id', type: 'string', format: 'uuid'),
        new OA\Property(property: 'name', type: 'string'),
        new OA\Property(property: 'email', type: 'string', format: 'email'),
        new OA\Property(property: 'phone', type: 'string', nullable: true),
        new OA\Property(property: 'address', type: 'string'),
        new OA\Property(property: 'deletedAt', type: 'string', format: 'date-time', nullable: true),
        new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
        new OA\Property(property: 'updatedAt', type: 'string', format: 'date-time'),
    ],
    example: [
        'id' => 'cff92407-d772-447f-af6b-d53722361948',
        'name' => 'ACME Holdings Ltd',
        'email' => '<EMAIL>',
        'phone' => '******-0123',
        'address' => '123 Business Ave, New York, NY 10001, US',
        'deletedAt' => null,
        'createdAt' => '2025-06-02T13:00:00Z',
        'updatedAt' => '2025-06-02T13:00:00Z',
    ]
)]
final readonly class GetOrganizationQueryResponse implements QueryResponseInterface
{
    public function __construct(
        private string $id,
        private string $name,
        private string $email,
        private ?string $phone,
        private string $address,
        private ?DateTimeInterface $deletedAt,
        private ?DateTimeInterface $createdAt,
        private ?DateTimeInterface $updatedAt
    ) {}

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getDeletedAt(): ?string
    {
        return $this->deletedAt?->format('c');
    }

    public function getCreatedAt(): ?string
    {
        return $this->createdAt?->format('c');
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updatedAt?->format('c');
    }
}
