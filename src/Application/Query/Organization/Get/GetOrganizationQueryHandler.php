<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\Get;

use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class GetOrganizationQueryHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $repository
    ) {}

    public function __invoke(GetOrganizationQuery $query): ?GetOrganizationQueryResponse
    {
        $organization = $this->repository->find($query->getId());
        
        if (null === $organization) {
            return null;
        }

        return new GetOrganizationQueryResponse(
            $organization->getId(),
            $organization->getName(),
            $organization->getEmail(),
            $organization->getPhone(),
            $organization->getAddress(),
            $organization->getDeletedAt(),
            $organization->getCreatedAt(),
            $organization->getUpdatedAt()
        );
    }
}
