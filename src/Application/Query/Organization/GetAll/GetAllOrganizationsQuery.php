<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\GetAll;

use App\Domain\Model\Bus\Query\QueryInterface;

final readonly class GetAllOrganizationsQuery implements QueryInterface
{
    public function __construct(
        private int $page = 1,
        private int $limit = 20,
        private array $filters = []
    ) {}

    public function getPage(): int
    {
        return $this->page;
    }

    public function getLimit(): int
    {
        return max(1, min(100, $this->limit)); // Ensure limit is between 1 and 100
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function getOffset(): int
    {
        return ($this->getPage() - 1) * $this->getLimit();
    }
}
