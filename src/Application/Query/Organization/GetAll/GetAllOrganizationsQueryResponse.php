<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\GetAll;

use OpenApi\Attributes as OA;
use App\Domain\Model\Bus\Query\QueryResponseInterface;
use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;

#[OA\Schema(
    schema: 'OrganizationsListResponse',
    type: 'object',
    required: ['organizations', 'pagination'],
    properties: [
        new OA\Property(
            property: 'organizations',
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/OrganizationResponse')
        ),
        new OA\Property(
            property: 'pagination',
            type: 'object',
            required: ['page', 'limit', 'total', 'totalPages'],
            properties: [
                new OA\Property(property: 'page', type: 'integer'),
                new OA\Property(property: 'limit', type: 'integer'),
                new OA\Property(property: 'total', type: 'integer'),
                new OA\Property(property: 'totalPages', type: 'integer'),
            ]
        )
    ],
    example: [
        'organizations' => [
            [
                'id' => 'cff92407-d772-447f-af6b-d53722361948',
                'name' => 'ACME Holdings Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Business Ave, New York, NY 10001, US',
                'deletedAt' => null,
                'createdAt' => '2025-06-02T13:00:00Z',
                'updatedAt' => '2025-06-02T13:00:00Z',
            ]
        ],
        'pagination' => [
            'page' => 1,
            'limit' => 20,
            'total' => 1,
            'totalPages' => 1,
        ]
    ]
)]
final readonly class GetAllOrganizationsQueryResponse implements QueryResponseInterface
{
    /**
     * @param GetOrganizationQueryResponse[] $organizations
     */
    public function __construct(
        private array $organizations,
        private int $page,
        private int $limit,
        private int $total
    ) {}

    /**
     * @return GetOrganizationQueryResponse[]
     */
    public function getOrganizations(): array
    {
        return $this->organizations;
    }

    public function getPagination(): array
    {
        return [
            'page' => $this->page,
            'limit' => $this->limit,
            'total' => $this->total,
            'totalPages' => (int) ceil($this->total / $this->limit),
        ];
    }
}
