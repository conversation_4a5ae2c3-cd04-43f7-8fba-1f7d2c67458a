<?php

declare(strict_types=1);

namespace App\Application\Query\Organization\GetAll;

use App\Application\Query\Organization\Get\GetOrganizationQueryResponse;
use App\Domain\Model\Organization\OrganizationRepositoryInterface;

final readonly class GetAllOrganizationsQueryHandler
{
    public function __construct(
        private readonly OrganizationRepositoryInterface $repository
    ) {}

    public function __invoke(GetAllOrganizationsQuery $query): GetAllOrganizationsQueryResponse
    {
        $organizations = $this->repository->findAllPaginated(
            $query->getOffset(),
            $query->getLimit(),
            $query->getFilters()
        );

        $total = $this->repository->countAll($query->getFilters());

        $organizationResponses = array_map(
            fn($organization) => new GetOrganizationQueryResponse(
                $organization->getId(),
                $organization->getName(),
                $organization->getEmail(),
                $organization->getPhone(),
                $organization->getAddress(),
                $organization->getDeletedAt(),
                $organization->getCreatedAt(),
                $organization->getUpdatedAt()
            ),
            $organizations
        );

        return new GetAllOrganizationsQueryResponse(
            $organizationResponses,
            $query->getPage(),
            $query->getLimit(),
            $total
        );
    }
}
