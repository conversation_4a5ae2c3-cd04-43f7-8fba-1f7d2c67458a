# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    app.service_name: 'tenants'
    app.env: '%env(APP_ENV)%'
    app.redis_dsn: '%env(REDIS_DSN)%'
    app.domain_api: '%env(DOMAIN_API)%'
    app.admin_routes_name_prefix: 'admin_'
    app.supported_locales: en|bg
    app.sensitive_data:
        - password
        - token

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $appServiceName: '%app.service_name%'
    
    get_set_method_normalizer:
        class: Symfony\Component\Serializer\Normalizer\GetSetMethodNormalizer
        tags: [ serializer.normalizer ]

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    command_handlers:
        namespace: App\Application\Command\
        resource: '%kernel.project_dir%/src/Application/Command/**/*CommandHandler.php'
        tags:
            - { name: messenger.message_handler, bus: command.bus }

    query_handlers:
        namespace: App\Application\Query\
        resource: '%kernel.project_dir%/src/Application/Query/**/*QueryHandler.php'
        tags:
            - { name: messenger.message_handler, bus: query.bus }

    event_handlers:
        namespace: App\Application\EventHandler\
        resource: '%kernel.project_dir%/src/Application/EventHandler/**/*Handler.php'
        tags:
            - { name: messenger.message_handler, bus: event.bus }

    App\Infrastructure\Delivery\Api\:
        resource: ../src/Infrastructure/Delivery/Api/
        tags: [ 'controller.service_arguments' ]

    App\Infrastructure\Delivery\Api\V1\GetDocumentationAction:
        tags: [ 'controller.service_arguments' ]
        arguments:
            $projectDir: '%kernel.project_dir%'
            
    App\Tests\Shared\Fixtures\:
        resource: '../tests/Shared/Fixtures/'

    monolog.formatter.line_formatter:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "[%%datetime%%] [%%extra.memory_peak_usage%%] [%%extra.correlation_id%%] %%channel%%.%%level_name%%: %%message%% %%context%% %%extra%%\n"

    messenger.transport.json_serializer:
        class: Symfony\Component\Messenger\Transport\Serialization\Serializer
        arguments:
            $serializer: '@rpc_serializer'
            $format: 'json'

    App\Infrastructure\Messenger\SerializerInterface:
        class: App\Infrastructure\Messenger\SymfonySerializer
        arguments:
            $serializer: '@rpc_serializer'

    rpc_serializer:
        class: 'Symfony\Component\Serializer\Serializer'
        autoconfigure: false
        factory: [ 'App\Infrastructure\Messenger\SymfonySerializerFactory', 'create' ]
        arguments:
            $taggedNormalizers: !tagged_iterator { tag: 'serializer.normalizer', default_priority_method: getPriority }
            $additionalNormalizers:
                - !service
                    class: 'Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer'
                - !service
                    class: 'Symfony\Component\Serializer\Normalizer\ObjectNormalizer'
                    arguments:
                        $propertyTypeExtractor: !service
                            class: 'Symfony\Component\PropertyInfo\PropertyInfoExtractor'
                            arguments:
                                $listExtractors:
                                    - !service
                                        class: 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor'
                                $typeExtractors:
                                    - !service
                                        class: 'Symfony\Component\PropertyInfo\Extractor\PhpStanExtractor'
                                    - !service
                                        class: 'Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor'
                                    - !service
                                        class: 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor'
                - !service
                    class: 'Symfony\Component\Serializer\Normalizer\ArrayDenormalizer'
            $encoders:
                - !service
                    class: 'Symfony\Component\Serializer\Encoder\JsonEncoder'

    App\Infrastructure\Rpc\Transport\RpcResultReceiverInterface:
        class: App\Infrastructure\Rpc\Transport\ExceptionRpcResultReceiver\ExceptionRpcResultReceiverDecorator
        arguments:
            $decoratedResultReceiver: '@App\Infrastructure\Rpc\Transport\Cache\CacheRpcResultReceiver'

    # Repository bindings
    App\Domain\Model\Organization\OrganizationRepositoryInterface:
        class: App\Infrastructure\Domain\Model\Organization\DoctrineOrganizationRepository

when@test:
    parameters:
        app.wrap_in_transaction: true
        app.redis_dsn: '%env(TEST_REDIS_DSN)%'

    services:
        # default configuration for services in *this* file
        _defaults:
            autowire: true      # Automatically injects dependencies in your services.
            autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

        App\Infrastructure\Rpc\Transport\RpcResultReceiverInterface:
            class: App\Tests\Double\Rpc\Transport\MockResultReceiver
            public: true

        App\Infrastructure\Rpc\Transport\RpcResultSenderInterface:
            class: App\Tests\Double\Rpc\Transport\MockResultSender
            public: true

        App\Tests\Integration\Infrastructure\Rpc\CompilerPass\Resources\:
            resource: ../tests/Integration/Infrastructure/Rpc/CompilerPass/Resources
        App\Tests\Functional\Infrastructure\Delivery\Rpc\Test\InvalidRequestAction:
            public: true
        App\Tests\Functional\Infrastructure\Delivery\Rpc\Test\InvalidParamsAction:
            public: true
